<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Schema\Blueprint;

class AddMcaaColumn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:add-mcaa-column';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add the core_group_mcaa column to system_controls table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking if core_group_mcaa column exists...');
        
        if (Schema::hasColumn('system_controls', 'core_group_mcaa')) {
            $this->info('✓ Column core_group_mcaa already exists.');
            return Command::SUCCESS;
        }
        
        $this->info('Adding core_group_mcaa column...');
        
        try {
            Schema::table('system_controls', function (Blueprint $table) {
                $table->integer('core_group_mcaa')->default(2)->after('core_group_caa');
            });
            
            $this->info('✓ Column core_group_mcaa added successfully.');
            
            // Verify the column was added
            if (Schema::hasColumn('system_controls', 'core_group_mcaa')) {
                $this->info('✓ Verification: Column exists and is ready to use.');
            } else {
                $this->error('✗ Verification failed: Column was not added properly.');
                return Command::FAILURE;
            }
            
        } catch (\Exception $e) {
            $this->error('✗ Error adding column: ' . $e->getMessage());
            
            // Try alternative approach with raw SQL
            $this->info('Trying alternative approach with raw SQL...');
            try {
                DB::statement('ALTER TABLE system_controls ADD COLUMN core_group_mcaa INT DEFAULT 2 AFTER core_group_caa');
                $this->info('✓ Column added using raw SQL.');
            } catch (\Exception $e2) {
                $this->error('✗ Alternative approach also failed: ' . $e2->getMessage());
                return Command::FAILURE;
            }
        }
        
        return Command::SUCCESS;
    }
}
