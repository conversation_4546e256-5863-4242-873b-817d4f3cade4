<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('missed_caa_count')->default(0)->after('is_core_auteur_candidate');
            $table->timestamp('last_caa_check_week')->nullable()->after('missed_caa_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('missed_caa_count');
            $table->dropColumn('last_caa_check_week');
        });
    }
};
