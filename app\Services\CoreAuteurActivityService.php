<?php

namespace App\Services;

use App\Models\User;
use App\Models\SystemControl;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CoreAuteurActivityService
{
    /**
     * Check Core Auteur activity for a user and handle warnings/badge removal
     *
     * @param User $user
     * @return array Returns array with 'warning_message' and 'remove_badge' flags
     */
    public function checkCoreAuteurActivity(User $user): array
    {
        $result = [
            'warning_message' => null,
            'remove_badge' => false
        ];

        // Only check for Core Auteur Candidates
        if (!$user->is_core_auteur_candidate) {
            return $result;
        }

        $systemControl = SystemControl::first();
        if (!$systemControl) {
            Log::warning('SystemControl not found for Core Auteur activity check');
            return $result;
        }

        $coreAuteurActivityThreshold = $systemControl->core_group_caa ?? 0;

        // Check if core_group_mcaa column exists, if not use default value
        $allowedMissings = 2; // Default value
        if (\Illuminate\Support\Facades\Schema::hasColumn('system_controls', 'core_group_mcaa')) {
            $allowedMissings = $systemControl->core_group_mcaa ?? 2;
        }

        // If CAA is 0, no activity constraints
        if ($coreAuteurActivityThreshold == 0) {
            return $result;
        }

        $currentWeek = now()->startOfWeek();
        $lastCheckWeek = $user->last_caa_check_week;

        // If this is the first check or we're in a new week
        if (!$lastCheckWeek || $lastCheckWeek->lt($currentWeek)) {
            // Check if user met the activity requirement for the previous week
            $previousWeekStart = $currentWeek->copy()->subWeek();
            
            // If this is the very first check, initialize tracking
            if (!$lastCheckWeek) {
                $user->last_caa_check_week = $currentWeek;
                $user->save();
                return $result;
            }

            // Check if user met activity requirement in the previous week
            $metRequirement = $this->didUserMeetActivityRequirement($user, $coreAuteurActivityThreshold, $previousWeekStart);

            if (!$metRequirement) {
                // User missed the requirement, increment missed count
                $user->missed_caa_count = ($user->missed_caa_count ?? 0) + 1;
                
                Log::info('Core Auteur Candidate missed weekly activity requirement', [
                    'user_id' => $user->id,
                    'missed_count' => $user->missed_caa_count,
                    'allowed_missings' => $allowedMissings,
                    'threshold' => $coreAuteurActivityThreshold
                ]);

                // Check if user exceeded allowed missings
                if ($user->missed_caa_count > $allowedMissings) {
                    // Remove Core Auteur Candidate status
                    $user->is_core_auteur_candidate = false;
                    $result['remove_badge'] = true;
                    
                    Log::info('Core Auteur Candidate badge removed due to excessive missed activity', [
                        'user_id' => $user->id,
                        'missed_count' => $user->missed_caa_count,
                        'allowed_missings' => $allowedMissings
                    ]);
                } else {
                    // Show warning message
                    $result['warning_message'] = "Dear Auteur, your Core Auteur Candidate Status is at risk because of low activity (less than \"{$coreAuteurActivityThreshold}\" active session(s) per week) so kindly keep up going back and be active as much as the current Vault allows so as not to lose a Core Auteur Candidate status)";
                }
            }

            // Update the last check week
            $user->last_caa_check_week = $currentWeek;
            $user->save();
        }

        return $result;
    }

    /**
     * Check if user met activity requirement for a specific week
     *
     * @param User $user
     * @param int $threshold
     * @param Carbon $weekStart
     * @return bool
     */
    private function didUserMeetActivityRequirement(User $user, int $threshold, Carbon $weekStart): bool
    {
        // Check if the user's last_login_week matches the week we're checking
        // and if their weekly_logins meets the threshold
        if ($user->last_login_week && $user->last_login_week->isSameWeek($weekStart)) {
            return $user->weekly_logins >= $threshold;
        }

        // If user didn't log in during that week at all, they didn't meet the requirement
        return false;
    }

    /**
     * Check if user qualifies for Core Auteur plan (used during production flow switch)
     *
     * @param User $user
     * @return bool
     */
    public function qualifiesForCoreAuteurPlan(User $user): bool
    {
        if (!$user->is_core_auteur_candidate) {
            return false;
        }

        $systemControl = SystemControl::first();
        if (!$systemControl) {
            return false;
        }

        $coreAuteurActivityThreshold = $systemControl->core_group_caa ?? 0;

        // Check if core_group_mcaa column exists, if not use default value
        $allowedMissings = 2; // Default value
        if (\Illuminate\Support\Facades\Schema::hasColumn('system_controls', 'core_group_mcaa')) {
            $allowedMissings = $systemControl->core_group_mcaa ?? 2;
        }

        // If CAA is 0, no activity constraints - user qualifies
        if ($coreAuteurActivityThreshold == 0) {
            return true;
        }

        // Check if user's missed count is within allowed limits
        $missedCount = $user->missed_caa_count ?? 0;
        
        // User qualifies if they haven't exceeded the allowed missings
        return $missedCount <= $allowedMissings;
    }

    /**
     * Get failure message for users who don't qualify for Core Auteur plan
     *
     * @return string
     */
    public function getCoreAuteurFailureMessage(): string
    {
        $systemControl = SystemControl::first();
        $coreAuteurActivityThreshold = $systemControl ? $systemControl->core_group_caa : 0;
        
        return "Dear Auteur, unfortunately you seemingly have failed consistently to meet a Core Auteur Candidate activity condition of at least \"{$coreAuteurActivityThreshold}\" active session(s) per week, so please kindly enjoy our AUTEUR Early Bird offer instead.";
    }
}
