<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\SystemControl;
use App\Models\Subscription;
use App\Services\SubscriptionFlowService;
use App\Services\PaddleService;
use Carbon\Carbon;

class ProfileController extends Controller
{
    protected $flowService;

    public function __construct(SubscriptionFlowService $flowService)
    {
        $this->flowService = $flowService;
    }
    /**
     * Show the user profile page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();

        // Get amazon reviewer name update interval setting
        $reviewerNameUpdateInterval = SystemControl::where('key_type', 'AmazonReviewerNameUpdateInterval')->first();
        $updateInterval = $reviewerNameUpdateInterval ? (int)$reviewerNameUpdateInterval->value : 14;

        // Calculate days remaining until user can update Amazon Reviewer Name
        $daysRemaining = 0;
        if ($updateInterval > 0 && $user->amazon_reviewer_name_updated_at) {
            $lastUpdate = Carbon::parse($user->amazon_reviewer_name_updated_at);
            $nextUpdateDate = $lastUpdate->addDays($updateInterval);
            if ($nextUpdateDate->isFuture()) {
                $daysRemaining = now()->diffInDays($nextUpdateDate);
            }
        }

        // Get user's active subscription
        $subscription = $user->activeSubscription();

        // Check if user has any subscription (active or not)
        $hasAnySubscription = $user->subscriptions()->exists();

        // We still need the subscription variable for other parts of the view
        // even if there's no active subscription

        // If no active subscription, log it but don't create one automatically
        if (!$subscription) {
            Log::info('No active subscription found for user', ['user_id' => $user->id]);

            // Set book and review limits for AUTEUR plan if not already set
            if (!$user->book_limit) {
                $user->book_limit = 3; // Default for AUTEUR
            }
            if (!$user->review_limit_per_week) {
                $user->review_limit_per_week = 3; // Default for AUTEUR
            }
            $user->save();
        } else {
            // CRITICAL FIX: Always ensure the plan type is correct based on product_id
            // This is a direct fix for the issue where plan types are not correctly displayed
            if ($subscription->product_id) {
                // Get the PaddleService to check plan type
                $paddleService = app(\App\Services\PaddleService::class);

                // Special handling for Alpha subscriptions
                if ($subscription->product_id === 'alpha' && $subscription->plan_type !== 'alpha') {
                    Log::info('Fixing Alpha plan type mismatch', [
                        'subscription_id' => $subscription->id,
                        'current_plan_type' => $subscription->plan_type,
                        'product_id' => $subscription->product_id
                    ]);

                    // Force update to Alpha
                    $subscription->update(['plan_type' => 'alpha']);
                    $paddleService->updateUserLimits($subscription);

                    // Refresh the subscription to get updated data
                    $subscription->refresh();
                }
                // Get all possible product/price IDs for comparison
                $auteurPlusProductId = config('services.paddle.products.auteur_plus');
                $auteurPlusPriceId = config('services.paddle.prices.auteur_plus');

                // Direct check for Auteur+ product IDs
                $isAuteurPlus = ($subscription->product_id === $auteurPlusProductId ||
                                 $subscription->product_id === $auteurPlusPriceId);

                // If this is an Auteur+ product but plan_type is not set correctly
                if ($isAuteurPlus && $subscription->plan_type !== 'auteur_plus') {
                    Log::info('Fixing Auteur+ plan type mismatch', [
                        'subscription_id' => $subscription->id,
                        'current_plan_type' => $subscription->plan_type,
                        'product_id' => $subscription->product_id,
                        'auteur_plus_product_id' => $auteurPlusProductId,
                        'auteur_plus_price_id' => $auteurPlusPriceId
                    ]);

                    // Force update to Auteur+
                    $subscription->update(['plan_type' => 'auteur_plus']);
                    $paddleService->updateUserLimits($subscription);

                    // Refresh the subscription to get updated data
                    $subscription->refresh();
                } else {
                    // For other cases, use the standard method
                    $expectedPlanType = $paddleService->determinePlanTypeFromProductId($subscription->product_id);

                    // If plan type doesn't match what we'd expect from the product ID, update it
                    if ($expectedPlanType !== $subscription->plan_type) {
                        Log::info('Correcting plan type mismatch in profile view', [
                            'subscription_id' => $subscription->id,
                            'current_plan_type' => $subscription->plan_type,
                            'expected_plan_type' => $expectedPlanType,
                            'product_id' => $subscription->product_id
                        ]);

                        $subscription->update(['plan_type' => $expectedPlanType]);
                        $paddleService->updateUserLimits($subscription);

                        // Refresh the subscription to get updated data
                        $subscription->refresh();
                    }
                }
            }
        }

        // Log subscription information for debugging
        Log::info('User subscription information', [
            'user_id' => $user->id,
            'has_active_subscription' => $user->hasActiveSubscription(),
            'active_subscription' => $subscription ? $subscription->toArray() : null
        ]);

        // Determine subscription plan types
        $isAuteurPlus = false;
        $isEarlyBird = false;
        if ($subscription) {
            // Use the plan_type field directly instead of comparing product IDs
            $isAuteurPlus = $subscription->isAuteurPlus();
            $isEarlyBird = $subscription->isEarlyBird();

            Log::info('User subscription plan type', [
                'user_id' => $user->id,
                'plan_type' => $subscription->plan_type,
                'is_auteur_plus' => $isAuteurPlus,
                'is_early_bird' => $isEarlyBird
            ]);
        }

        // Get the current flow state
        $currentFlow = $this->flowService->getCurrentFlow();
        $productionFlow = $currentFlow;

        // Check if vault access should be restricted
        $vaultRestricted = false;
        if ($subscription && $subscription->isAlpha() && $currentFlow === 'production' && $this->flowService->isProductionCountdownExpired()) {
            $vaultRestricted = true;
        }

        // Core Group Upgrade Logic
        $showCoreAuteurUpgrade = session('core_auteur_upgrade_offer', false);
        $showEarlyBirdOffer = session('early_bird_offer', false);
        session()->forget(['core_auteur_upgrade_offer', 'early_bird_offer']);

        // Retrieve core_group_caa for display in the message
        $systemControl = SystemControl::first();
        $coreGroupCaa = $systemControl ? $systemControl->core_group_caa : 0;

        // The existing logic for showCoreAuteurUpgrade was based on direct calculation.
        // Now it's driven by the flash message from UserController.
        // However, we still need to ensure the user doesn't already have the subscription.
        if ($showCoreAuteurUpgrade) {
            if ($user->hasActiveSubscription() &&
                ($user->activeSubscription()->isAuteurPlus() || $user->activeSubscription()->isCoreAuteur())) {
                $showCoreAuteurUpgrade = false; // Already has the upgrade
            }
        }

        return view('profile.index', compact(
            'user',
            'updateInterval',
            'daysRemaining',
            'subscription',
            'hasAnySubscription',
            'isAuteurPlus',
            'isEarlyBird',
            'currentFlow',
            'productionFlow',
            'vaultRestricted',
            'showCoreAuteurUpgrade',
            'showEarlyBirdOffer',
            'coreGroupCaa' // Pass coreGroupCaa to the view
        ));
    }

    /**
     * Update the user's basic profile information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email|max:255|unique:users,email,' . Auth::id(),
        ]);

        $user = Auth::user();
        $user->email = $request->email;
        $user->save();

        return redirect()->route('profile.index')->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's Amazon Reviewer Name
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAmazonReviewerName(Request $request)
    {
        $request->validate([
            'amazon_reviewer_name' => 'nullable|string|max:255',
        ]);

        $user = Auth::user();

        // Get amazon reviewer name update interval setting
        $reviewerNameUpdateInterval = SystemControl::where('key_type', 'AmazonReviewerNameUpdateInterval')->first();
        $updateInterval = $reviewerNameUpdateInterval ? (int)$reviewerNameUpdateInterval->value : 14;

        // Check if user can update Amazon Reviewer Name
        if ($updateInterval > 0 && $user->amazon_reviewer_name_updated_at) {
            $lastUpdate = Carbon::parse($user->amazon_reviewer_name_updated_at);
            $nextUpdateDate = $lastUpdate->addDays($updateInterval);

            if ($nextUpdateDate->isFuture()) {
                $daysRemaining = now()->diffInDays($nextUpdateDate);
                return redirect()->route('profile.index')
                    ->withErrors(['amazon_reviewer_name' => "Please wait {$daysRemaining} day(s) until Your Amazon Reviewer Name can be updated"]);
            }
        }

        $user->amazon_reviewer_name = $request->amazon_reviewer_name;
        $user->amazon_reviewer_name_updated_at = now();
        $user->save();

        return redirect()->route('profile.index')->with('success', 'Amazon Reviewer Name updated successfully!');
    }

    /**
     * Update the user's password
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8',
            'new_password_confirmation' => 'required|same:new_password',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect']);
        }

        $user->password = Hash::make($request->new_password);
        $user->save();

        return redirect()->route('profile.index')->with('success', 'Password updated successfully!');
    }
}
