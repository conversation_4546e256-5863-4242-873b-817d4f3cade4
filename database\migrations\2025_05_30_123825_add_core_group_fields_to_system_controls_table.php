<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('system_controls', function (Blueprint $table) {
            $table->integer('core_group_can')->default(100); // Core Auteurs Number
            $table->integer('core_group_caa')->default(0); // Core Auteur Activity (logins per week)
            $table->integer('core_group_mcaa')->default(2); // CAA ALLOWED MISSINGS
            $table->boolean('core_group_active')->default(false); // Checkbox to start/stop feature
            $table->integer('core_group_registered_count')->default(0); // Number of users registered while feature was active
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('system_controls', function (Blueprint $table) {
            $table->dropColumn('core_group_can');
            $table->dropColumn('core_group_caa');
            $table->dropColumn('core_group_mcaa');
            $table->dropColumn('core_group_active');
            $table->dropColumn('core_group_registered_count');
        });
    }
};
