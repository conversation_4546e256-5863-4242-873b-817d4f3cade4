@extends('master-layout.master-layout');
<link rel="stylesheet" href="{{ asset('admin/css/style.css') }}">
@section('page_content')
    <div class="container">
        <section class="padding-medium text-center">
            <h2>Book Economy</h2>
        </section>
    </div>
    <div class="dashboard">
        <div class="container-fluid p-5">
            <div class="row">
                <div class="col-md-3">
                    @include('admin.includes.navigation')
                </div>
                <div class="col-md-9">
                    <div class="content-panel">

                        <form action="{{ route('updateBookEconomy') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <div class="row mb-4">
                                    <div class="col-6">
                                        <h5 class="mb-0">How Many Assignments At A Time?</h5>
                                        <p class="mb-3">How many assignments a person can do at a time?</p>
                                        <div class="form-group">
                                            <input type="text" class="form-control" id="ActiveAssignments"
                                                name="numberOfAssignments" value="{{ $assignment->value }}"
                                                placeholder="Enter Number Of Assignments">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="mb-0">Points Cut?</h5>
                                        <p class="mb-3">How much points system will charge on boosted Tokens?</p>
                                        <div class="form-group">
                                            <input type="text" class="form-control" id="ActiveAssignments"
                                                name="commisionValue" placeholder="Enter Number Of Assignments"
                                                value="{{ $commission->value }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row ">
                                    <div class="col-6 mb-4">
                                        <h5 class="mb-0">Turnaround Time Points?</h5>
                                        <p class="mb-3">Select Turnaround time and enter points how much should system
                                            charge?
                                        </p>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-8">
                                                    <select name="selectTurnAroundTime" class="form-select"
                                                        id="SelectTurnAroundTime">
                                                        @foreach ($turnArounodTime as $time)
                                                            <option value="{{ $time->key }}">{{ $time->key }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-4">
                                                    <input type="text" class="form-control" name="turnAroundTime"
                                                        id="TurnAroundTimeValue" placeholder="Enter Points"
                                                        value="{{ $turnArounodTime[0]->value }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-4">
                                        <h5 class="mb-0">Verified Purchase?</h5>
                                        <p class="mb-3">Select Verified Purchase and enter points how much should system
                                            charge?</p>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-8">
                                                    <select name="selectVirtualPoints" id="selectVirtualPoints"
                                                        class="form-select">
                                                        @foreach ($virtualPoints as $vp)
                                                            <option value="{{ $vp->key }}">{{ $vp->key }}
                                                            </option>
                                                        @endforeach

                                                    </select>
                                                </div>
                                                <div class="col-4">
                                                    <input type="text" class="form-control"
                                                        name="selectVirtualPointsValue" id="selectVirtualPointsValue"
                                                        placeholder="Enter Points" value="{{ $virtualPoints[0]->value }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {{-- <div class="col-6 mb-4">
                                    <h5 class="mb-0">Free Boosting Points?</h5>
                                    <p class="mb-3">How many points reviewer get when he leave a review?</p>
                                    <div class="form-group">
                                        <input type="text" class="form-control" name="freeBoostingPoints"
                                            id="freeBoostingPoints" placeholder="Enter Points"
                                            value="{{ $freeBoostingPoints->value }}">
                                    </div>
                                </div> --}}

                                </div>

                                <div class="row">
                                    <div class="col-6 mb-4">
                                        <div class="form-group">
                                            <h5 class="mb-0">Kindle Unlimited?</h5>
                                            <p class="mb-3">Enter Amount You Will Charge For Kindle Unlimited?</p>
                                            <input type="text" name="kindleUnlimited" value="{{ $KindlePoints->value }}"
                                                class="form-control">
                                        </div>
                                    </div>

                                    <div class="col-6 mb-4">
                                        <div class="form-group">
                                            <h5 class="mb-0">Initial Points (Percentage) </h5>
                                            <p class="mb-3">Enter Amount User Will Get When They Take Book For Review.</p>
                                            <input type="text" name="InitialPoints"
                                                value="{{ $initialPoints->value }}" class="form-control">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <div class="form-group mb-4">
                                    <h5 class="mb-0">Auto Review Validation Settings</h5>
                                    <p class="mb-3">Configure when the automatic review validation process starts and ends</p>
                                </div>

                                <div class="form-group mb-4">
                                    <h5 class="mb-0">Manual Rejection Verification Mode</h5>
                                    <p class="mb-3">When checked, reviews not found after end time will require manual verification instead of being auto-rejected</p>
                                </div>

                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" name="autoManualApprove" id="manualRejectionVerification"
                                        {{ $autoManualApprove->value == 0 ? 'checked' : '' }} />
                                    <label class="form-check-label" for="manualRejectionVerification">
                                        Manual Rejection Verification
                                    </label>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <h5 class="mb-0">Autocheck Starts</h5>
                                            <p class="mb-3">Number of hours after review submission when autocheck starts (default: 72)<br>
                                            0 - Immediate check, Blank - No automatic check</p>
                                            <input type="number" class="form-control" name="day_autocheck_starts"
                                                value="{{ $autocheckStart->value ?? '72' }}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <h5 class="mb-0">Autocheck Ends</h5>
                                            <p class="mb-3">Number of hours after review submission when autocheck ends (default: 240)<br>
                                            0 - Immediate end-check, Blank - No automatic check</p>
                                            <input type="number" class="form-control" name="day_autocheck_ends"
                                                value="{{ $autocheckEnd->value ?? '240' }}" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <h5 class="mb-0">Autocheck Script Rate</h5>
                                            <p class="mb-3">Number of hours between consecutive autocheck runs (default: 24)</p>
                                            <input type="number" class="form-control" name="autocheck_rate"
                                                value="{{ $autocheckRate->value ?? '24' }}" min="1">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <div class="row mb-4">
                                    <div class="col">
                                        <div class="form-group">
                                            <h5 class="mb-0">MCQ to a Reviewer</h5>
                                            <p class="mb-3">Enter Number of Multiples Choice Questions to Be GIVEN to a REVIEWER
                                            </p>
                                            <input type="text" name="minmcqs"
                                                id="minmcqs" value="{{ $minmcqs->value }}"
                                                class="form-control">
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-group">
                                            <h5 class="mb-0">True/False to a Reviewer</h5>
                                            <p class="mb-3">Enter Number of True/False Questions to Be GIVEN to a REVIEWER
                                            </p>
                                            <input type="text" name="mintruefalse"
                                                id="mintruefalse" value="{{ $mintruefalse->value }}"
                                                class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col">
                                        <div class="form-group ">
                                            <h5 class="mb-0">MCQ from Author</h5>
                                            <p class="mb-3">Enter Number of Multiple Choice Question to be ENTERED by Author
                                            </p>
                                            <input type="text" name="multipleChoiceQuestion"
                                                id="multipleChoiceQuestion" value="{{ $multipleChoiceQuestions->value }}"
                                                class="form-control">
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-group ">
                                            <h5 class="mb-0">True/False from Author</h5>
                                            <p class="mb-3">Enter Number of True/False Question to be ENTERED by Author
                                            </p>
                                            <input type="text" name="TrueFalseQuetions" id="TrueFalseQuetions"
                                                value="{{ $TrueFalseQuetions->value }}" class="form-control">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group ">
                                        <h5 class="mb-0">Set Percentages</h5>
                                        <p class="mb-3">Select the Number of Correct Answers from the left Drop-down Menu, and set the Award Percentage On the Right.</p>
                                    </div>
                                    <div class="col-10">
                                        <select class="form-select" name="questionsPercentage" id="questionsPercentage">
                                            @php
                                                $totalQuestions = $minmcqs->value + $mintruefalse->value;
                                                $questionsArray = range(1, $totalQuestions);
                                            @endphp
                                            @foreach ($questionsArray as $item)
                                                <option value="{{ $item }}">{{ $item }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-2">
                                        <input type="number" class="form-control" name="questionPercentageValue"
                                            id="questionPercentageValue"
                                            value="{{ $questionPercentageValue[0]->value }}">
                                    </div>
                                </div>
                            </div>

                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <div class="form-group">
                                    <h5 class="mb-0">SignUp Bonus</h5>
                                    <p>Enter number of token user will be given when they signup.</p>
                                    <input type="text" name="SignUpBonus"
                                        value="{{ $SignUpBonus->value }}" class="form-control">
                                </div>
                            </div>

                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <div class="form-group mb-4">
                                    <h5 class="mb-0">Subscription Flow Settings</h5>
                                    <p class="mb-3">Configure the subscription flow (Alpha or Production)</p>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="alert {{ $subscriptionFlow && (int)$subscriptionFlow->value === 1 ? 'alert-success' : 'alert-info' }}">
                                            <strong>Current Flow:</strong> {{ $subscriptionFlow ? ((int)$subscriptionFlow->value === 1 ? 'Production' : 'Alpha') : 'Alpha' }} Mode
                                            @if($countdownStarted)
                                                <div class="mt-2">
                                                    <strong>Production Mode Countdown:</strong>
                                                    @php
                                                        // Use the variables passed from the controller
                                                        $formattedHours = str_pad($remainingHours, 2, '0', STR_PAD_LEFT);
                                                        $formattedMinutes = str_pad($remainingMinutes, 2, '0', STR_PAD_LEFT);
                                                        $formattedSeconds = str_pad($remainingSecondsDisplay, 2, '0', STR_PAD_LEFT);
                                                    @endphp
                                                    {{ $formattedHours }}:{{ $formattedMinutes }}:{{ $formattedSeconds }} remaining
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="switch_to_production" id="switchToProduction"
                                                {{ $subscriptionFlow && (int)$subscriptionFlow->value === 1 ? 'checked disabled' : '' }} />
                                            <label class="form-check-label" for="switchToProduction">
                                                Switch to Production Flow
                                            </label>
                                        </div>

                                        <div id="productionCountdownContainer" class="mb-3" {{ $subscriptionFlow && (int)$subscriptionFlow->value === 1 ? 'style="display:none;"' : '' }}>
                                            <label for="productionCountdownHours" class="form-label">Switch to Production in (hours):</label>
                                            <input type="number" class="form-control" id="productionCountdownHours" name="production_countdown_hours"
                                                value="{{ $productionCountdownHours ? (int)$productionCountdownHours->value : '48' }}" min="0" {{ $subscriptionFlow && (int)$subscriptionFlow->value === 1 ? 'disabled' : '' }}>
                                            <small class="form-text text-muted">Set to 0 for immediate switch to Production mode</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="switch_to_alpha" id="switchToAlpha"
                                                {{ !$subscriptionFlow || (int)$subscriptionFlow->value === 0 ? 'disabled' : '' }} />
                                            <label class="form-check-label" for="switchToAlpha">
                                                Switch back to Alpha Flow (for testing purposes)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <div class="form-group mb-4">
                                    <h5 class="mb-0">Number of Books Taken Per Week</h5>
                                    <p>Enter the total number of books a user is allowed to take for a review per week.</p>
                                    <input type="number" name="NumberOfBooksTakenPerWeek" value="{{ $BooksTakenPerWeek->value ?? '' }}" class="form-control">
                                </div>
                                 <div class="form-group">
                                    <h5 class="mb-0">Review Time Limit</h5>
                                    <p>Enter number of hours can be taken in between book and review.</p>
                                    <input type="text" name="ReviewTimeLimit"
                                        value="{{ $ReviewTimeLimit->value }}" class="form-control">
                                </div>
                                <div class="form-group mt-4">
                                    <h5 class="mb-0">Overdue Assignment Cancellation</h5>
                                    <p>Enter number of hours after which an overdue assignment will be automatically cancelled.</p>
                                    <input type="text" name="overdue_cancel_hours"
                                        value="{{ $overdueCancelHours->value }}" class="form-control">
                                </div>
                                <div class="form-group mt-4">
                                    <h5 class="mb-0">Amazon Reviewer Name Update Interval</h5>
                                    <p>Number of days to pass before an "Amazon Reviewer Name" could be updated/changed by user (default: 14, no limits: 0)</p>
                                    <input type="number" name="AmazonReviewerNameUpdateInterval"
                                        value="{{ $reviewerNameUpdateInterval->value ?? '14' }}" class="form-control">
                                </div>
                            </div>

                            <!-- Core Group Section -->
                            <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                                <h5 class="mb-4">Core Group Settings</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <h6 class="mb-0">Core Auteurs Number (CAN)</h6>
                                            <p class="mb-3">Total number of Core Auteur feature slots available</p>
                                            <input type="number" class="form-control" name="core_group_can"
                                                value="{{ $systemControl->core_group_can ?? 100 }}"
                                                placeholder="Enter Core Auteurs Number (default: 100)">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <h6 class="mb-0">Core Auteur Activity (CAA)</h6>
                                            <p class="mb-3">Number of logins per week required to qualify for Core Auteur plan (0 = no constraints)</p>
                                            <input type="number" class="form-control" name="core_group_caa"
                                                value="{{ $systemControl->core_group_caa ?? 0 }}"
                                                placeholder="Enter weekly login requirement (default: 0)">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <h6 class="mb-0">CAA ALLOWED MISSINGS (MCAA)</h6>
                                            <p class="mb-3">Number of weeks a Core Auteur Candidate can miss the activity requirement before losing status</p>
                                            <input type="number" class="form-control" name="core_group_mcaa"
                                                value="{{ $systemControl->core_group_mcaa ?? 2 }}"
                                                placeholder="Enter allowed missings (default: 2)">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <!-- Empty column for spacing -->
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="core_group_active" id="coreGroupActive"
                                                {{ $systemControl && $systemControl->core_group_active ? 'checked' : '' }} />
                                            <label class="form-check-label" for="coreGroupActive">
                                                <strong>Activate Core Group Feature</strong>
                                            </label>
                                            <p class="text-muted mt-2">Enable the Core Group feature to start recruiting Core Auteurs</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <h6 class="mb-0">Current Status</h6>
                                            <p class="mb-1">Registered Count: <strong>{{ $systemControl->core_group_registered_count ?? 0 }}</strong></p>
                                            <p class="mb-1">Slots Remaining: <strong>{{ max(0, ($systemControl->core_group_can ?? 100) - ($systemControl->core_group_registered_count ?? 0)) }}</strong></p>
                                            <p class="mb-0">Status:
                                                @if($systemControl && $systemControl->core_group_active)
                                                    <span class="text-success">Active</span>
                                                @else
                                                    <span class="text-muted">Inactive</span>
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button class="btn btn-primary w-100 py-3" type="submit">Update System</button>
                        </form>
                    </div>
                </div>
            </div>
        @endsection
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                let TurnAroundTime = document.querySelector('#SelectTurnAroundTime');
                TurnAroundTime.addEventListener('change', () => {
                    let xhr = new XMLHttpRequest();
                    let url = '/admin-book-economy/TURNAROUND';
                    let data = 'turnAroundTime=' + TurnAroundTime.value; // Corrected the data format
                    let csrfToken = '{{ csrf_token() }}';

                    xhr.open('POST', url, true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken);


                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === XMLHttpRequest.DONE) {
                            if (xhr.status === 200) {
                                let TurnAroundTImeValue = document.querySelector('#TurnAroundTimeValue');
                                TurnAroundTImeValue.value = xhr.responseText
                            } else {
                                console.log(xhr.statusText); // Corrected the error handling
                            }
                        }
                    };

                    xhr.send(data);
                });

                let selectVirtualPoints = document.querySelector('#selectVirtualPoints');
                selectVirtualPoints.addEventListener('change', () => {
                    let xhr = new XMLHttpRequest();
                    let url = '/admin-book-economy/TURNAROUND';
                    let data = 'selectVirtualPoints=' + selectVirtualPoints.value; // Corrected the data format
                    let csrfToken = '{{ csrf_token() }}';

                    xhr.open('POST', url, true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken);


                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === XMLHttpRequest.DONE) {
                            if (xhr.status === 200) {
                                let selectVirtualPointsValue = document.querySelector(
                                    '#selectVirtualPointsValue');
                                selectVirtualPointsValue.value = xhr.responseText
                            } else {
                                console.log(xhr.statusText); // Corrected the error handling
                            }
                        }
                    };

                    xhr.send(data); // Added this line to send the data
                })





                let MultipleChoiceQuestions = document.querySelector('#multipleChoiceQuestion');
                let TrueFalseQuestions = document.querySelector('#TrueFalseQuetions');

                MultipleChoiceQuestions.addEventListener('input', function() {
                    updateSelectInput();
                })

                TrueFalseQuestions.addEventListener('input', function() {
                    updateSelectInput();
                })


                function updateSelectInput() {
                    let MultipleChoiceValue = MultipleChoiceQuestions.value;
                    let TruefalseValue = TrueFalseQuestions.value;

                    let TotalQuestions = parseInt(MultipleChoiceValue) + parseInt(TruefalseValue);

                    console.log(TotalQuestions);
                }


                $('#questionsPercentage').on('change', function() {
                    $.ajax({
                        url: '/admin-book-economy/Percentage',
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        data: {
                            questionsPer: $(this).val()
                        },
                        success: function(res) {
                            if (res) {
                                $('#questionPercentageValue').val(res);
                            }
                        },
                        error: function(err) {
                            console.log(err)
                        }
                    })
                })

                const mcqInput = document.getElementById('minmcqs');
                const tfInput = document.getElementById('mintruefalse');
                const percentageSelect = document.getElementById('questionsPercentage');

                function updatePercentageOptions() {
                    const mcqValue = parseInt(mcqInput.value) || 0;
                    const tfValue = parseInt(tfInput.value) || 0;
                    const totalQuestions = mcqValue + tfValue;

                    // Clear existing options
                    percentageSelect.innerHTML = '';

                    // Add new options
                    for (let i = 1; i <= totalQuestions; i++) {
                        const option = document.createElement('option');
                        option.value = i;
                        option.textContent = i;
                        percentageSelect.appendChild(option);
                    }
                }

                mcqInput.addEventListener('input', updatePercentageOptions);
                tfInput.addEventListener('input', updatePercentageOptions);

                // Debug logging for verification mode switch
                const verificationModeSwitch = document.getElementById('manualRejectionVerification');
                if (verificationModeSwitch) {
                    console.log('Initial Manual Rejection Verification mode: ' +
                        (verificationModeSwitch.checked ? 'ACTIVE (0) - Reviews will require manual verification' :
                                                         'INACTIVE (1) - Reviews will be auto-rejected'));

                    verificationModeSwitch.addEventListener('change', function() {
                        console.log('Manual Rejection Verification mode changed to: ' +
                            (this.checked ? 'ACTIVE (0) - Reviews will require manual verification' :
                                           'INACTIVE (1) - Reviews will be auto-rejected'));
                    });
                }

                // Subscription flow controls
                const switchToProductionCheckbox = document.getElementById('switchToProduction');
                const switchToAlphaCheckbox = document.getElementById('switchToAlpha');
                const productionCountdownContainer = document.getElementById('productionCountdownContainer');
                const productionCountdownHours = document.getElementById('productionCountdownHours');

                // Log the current flow state for debugging
                console.log('Current flow state:', {
                    'switchToProduction': switchToProductionCheckbox ? switchToProductionCheckbox.checked : 'not found',
                    'switchToAlpha': switchToAlphaCheckbox ? switchToAlphaCheckbox.checked : 'not found',
                    'countdownHours': productionCountdownHours ? productionCountdownHours.value : 'not found'
                });

                if (switchToProductionCheckbox) {
                    // Show/hide countdown container based on checkbox state
                    switchToProductionCheckbox.addEventListener('change', function() {
                        if (this.checked) {
                            productionCountdownContainer.style.display = 'block';
                        } else {
                            productionCountdownContainer.style.display = 'none';
                        }
                    });

                    // Initialize display state based on checkbox
                    if (switchToProductionCheckbox.checked) {
                        productionCountdownContainer.style.display = 'block';
                    } else {
                        productionCountdownContainer.style.display = 'none';
                    }
                }

                // Check flow status periodically if countdown is active
                @if($countdownStarted)
                let countdownTimer;
                let countdownHours = {{ $remainingHours }};
                let countdownMinutes = {{ $remainingMinutes }};
                let countdownSeconds = {{ $remainingSecondsDisplay }};

                function updateCountdownDisplay() {
                    // Format the time values (add leading zeros if needed)
                    const formattedHours = String(countdownHours).padStart(2, '0');
                    const formattedMinutes = String(countdownMinutes).padStart(2, '0');
                    const formattedSeconds = String(countdownSeconds).padStart(2, '0');

                    // Update the countdown display
                    $('.alert strong:contains("Production Mode Countdown")').parent().html(
                        '<strong>Production Mode Countdown:</strong> ' +
                        formattedHours + ':' + formattedMinutes + ':' + formattedSeconds + ' remaining'
                    );
                }

                function decrementCountdown() {
                    if (countdownSeconds > 0) {
                        countdownSeconds--;
                    } else {
                        if (countdownMinutes > 0) {
                            countdownMinutes--;
                            countdownSeconds = 59;
                        } else {
                            if (countdownHours > 0) {
                                countdownHours--;
                                countdownMinutes = 59;
                                countdownSeconds = 59;
                            } else {
                                // CRITICAL FIX: Handle countdown expiration more gracefully
                                // Instead of reloading immediately, update the UI to show Production mode
                                clearInterval(countdownTimer);

                                // Update the alert to show Production mode is active
                                $('.alert:contains("Current Flow")').removeClass('alert-info').addClass('alert-success')
                                    .html('<strong>Current Flow:</strong> Production Mode');

                                // Disable the switch to production checkbox
                                $('#switchToProduction').prop('checked', true).prop('disabled', true);

                                // Enable the switch to alpha checkbox
                                $('#switchToAlpha').prop('disabled', false);

                                // Hide the countdown container
                                $('#productionCountdownContainer').hide();

                                // Check flow status once to see if we need to reload
                                checkFlowStatus();
                                return;
                            }
                        }
                    }

                    updateCountdownDisplay();
                }

                function checkFlowStatus() {
                    $.ajax({
                        url: '{{ route("checkFlowStatus") }}',
                        method: 'GET',
                        success: function(response) {
                            // CRITICAL FIX: Handle countdown expiration more gracefully
                            if (response.countdown_expired) {
                                // Instead of reloading immediately, update the UI to show Production mode
                                clearInterval(countdownTimer);

                                // Update the alert to show Production mode is active
                                $('.alert:contains("Current Flow")').removeClass('alert-info').addClass('alert-success')
                                    .html('<strong>Current Flow:</strong> Production Mode');

                                // Disable the switch to production checkbox
                                $('#switchToProduction').prop('checked', true).prop('disabled', true);

                                // Enable the switch to alpha checkbox
                                $('#switchToAlpha').prop('disabled', false);

                                // Hide the countdown container
                                $('#productionCountdownContainer').hide();

                                // Only reload if explicitly requested by the server
                                if (response.switch_needed) {
                                    // Wait 3 seconds before reloading to allow the user to see the UI changes
                                    setTimeout(function() {
                                        location.reload();
                                    }, 3000);
                                }
                            } else if (response.hours !== null && response.minutes !== null && response.seconds !== null) {
                                // Update our local countdown values with the server values
                                countdownHours = response.hours;
                                countdownMinutes = response.minutes;
                                countdownSeconds = response.seconds;
                                updateCountdownDisplay();
                            }
                        },
                        error: function(err) {
                            console.error('Error checking flow status:', err);
                        }
                    });
                }

                // Initialize the countdown display
                updateCountdownDisplay();

                // Update the countdown every second
                countdownTimer = setInterval(decrementCountdown, 1000);

                // Sync with the server every minute to prevent drift
                setInterval(checkFlowStatus, 60000);
                @endif
            });
        </script>
