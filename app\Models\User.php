<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Services\SubscriptionFlowService;
use Illuminate\Support\Facades\Log;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_admin',
        'fullName',
        'zipCode',
        'amazon_reviewer_name',
        'book_limit',
        'review_limit_per_week',
        'is_core_auteur_candidate',
        'weekly_logins',
        'last_login_week',
        'missed_caa_count',
        'last_caa_check_week',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_admin' => 'boolean',
        'is_core_auteur_candidate' => 'boolean',
        'weekly_logins' => 'integer',
        'last_login_week' => 'datetime',
        'missed_caa_count' => 'integer',
        'last_caa_check_week' => 'datetime',
    ];

    public function wallet() {
        return $this->hasOne(Wallet::class, 'userId');
    }

    public function subscriptions() {
        return $this->hasMany(Subscription::class);
    }

    public function activeSubscription() {
        return $this->subscriptions()->where('status', 'active')->latest()->first();
    }

    public function hasActiveSubscription() {
        $subscription = $this->activeSubscription();

        // If no subscription, return false
        if ($subscription === null) {
            return false;
        }

        // Check if we need to consider the flow state
        if ($subscription->isAlpha()) {
            try {
                // Get the flow service
                $flowService = app(SubscriptionFlowService::class);

                // Check if we're in production flow
                if ($flowService->isProductionFlow()) {
                    // Check if the countdown has expired
                    if ($flowService->isProductionCountdownExpired()) {
                        // In production with expired countdown, Alpha users need to upgrade
                        return false;
                    }
                }
            } catch (\Exception $e) {
                // If there's an error, log it and default to considering the subscription active
                \Log::error('Error checking subscription flow state', [
                    'error' => $e->getMessage(),
                    'user_id' => $this->id
                ]);
            }
        }

        return true;
    }
}
