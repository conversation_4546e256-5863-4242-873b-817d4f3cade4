<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class SystemControl extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'key',
        'key_type',
        'value',
        'created_at',
        'updated_at',
        'core_group_can',
        'core_group_caa',
        'core_group_mcaa',
        'core_group_active',
        'core_group_registered_count'
    ];
    
    protected $casts = [
        // Don't cast value to integer as some values might be strings
    ];
    
    public function setValueAttribute($value)
    {
        \Log::debug('SystemControl setValueAttribute called', ['incoming_value' => $value]);
        
        // Store the value as is - don't force conversion to integer
        $this->attributes['value'] = $value;
    }
    
}
