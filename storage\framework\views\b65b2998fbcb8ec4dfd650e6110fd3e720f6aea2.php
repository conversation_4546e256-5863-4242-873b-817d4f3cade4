

<?php $__env->startSection('page_title', 'User Profile'); ?>
<?php $__env->startSection('page_content'); ?>
<div class="container py-5">
    <div class="row">
        <!-- Left sidebar navigation -->
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">User Profile</div>
                <div class="list-group list-group-flush">
                    <a href="#profile-section" class="list-group-item list-group-item-action active">Profile</a>
                    <a href="#security-section" class="list-group-item list-group-item-action">Security</a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">Billing</div>
                <div class="list-group list-group-flush">
                    <a href="#subscription-section" class="list-group-item list-group-item-action">Subscription</a>
                    <a href="#payment-method-section" class="list-group-item list-group-item-action">Payment Method</a>
                    <a href="#invoices-section" class="list-group-item list-group-item-action">Invoices History</a>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-9">
            <!-- Flash messages are now handled by master layout -->

            <!-- Profile Information Section -->
            <div class="card mb-4" id="profile-section">
                 <div class="card-header">
                    <h1 class="CreateBookFields">Profile</h1>
                </div>
                <div class="card-body">
                    <!-- Contact Information Section -->
                    <div class="mb-4">
                        <h3 class="CreateBookFields">Contact Information</h3>
                        <form action="<?php echo e(route('profile.update')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="mb-3">
                                <label for="fullName" class="BookFieldsExplain">Name</label>
                                <div class="d-flex align-items-center">
                                    <input type="text" class="form-control <?php $__errorArgs = ['fullName'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="fullName" name="fullName" value="<?php echo e(old('fullName', $user->fullName)); ?>" readonly style="color: #6c757d; background-color: #e9ecef;">
                                    <?php if($subscription && $subscription->isCoreAuteur()): ?>
                                        <span class="badge bg-success ms-2" title="Core Auteur - Premium Member">🎉 Core Auteur</span>
                                    <?php elseif($user->is_core_auteur_candidate): ?>
                                        <span class="badge bg-info ms-2" title="Core Auteur Candidate">Core Auteur Candidate</span>
                                    <?php endif; ?>
                                </div>
                                <?php $__errorArgs = ['fullName'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="BookFieldsExplain">E-Mail Address</label>
                                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>">
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">Update Contact Info</button>
                            </div>
                        </form>
                    </div>

                    <!-- Amazon Reviewer Name Section -->
                    <div>
                        <h3 class="CreateBookFields">Your Amazon Reviewer Name</h3>
                        <form action="<?php echo e(route('profile.amazon_reviewer')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="amazon_reviewer_name" class="BookFieldsExplain">
                                        Amazon Reviewer Name <?php echo e($updateInterval > 0 ? "(can be updated once per {$updateInterval} days)" : ""); ?>

                                    </label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['amazon_reviewer_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="amazon_reviewer_name" name="amazon_reviewer_name" value="<?php echo e(old('amazon_reviewer_name', $user->amazon_reviewer_name)); ?>">
                                    <?php if($daysRemaining > 0): ?>
                                        <div class="text-danger mt-1">
                                            Please wait <?php echo e($daysRemaining); ?> day(s) until Your Amazon Reviewer Name can be updated
                                        </div>
                                    <?php endif; ?>
                                    <?php $__errorArgs = ['amazon_reviewer_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="text-end mt-3">
                                <button type="submit" class="btn btn-primary">Update Amazon Reviewer Name</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div class="card" id="security-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Security</h3>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('profile.password')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="mb-3">
                            <label for="current_password" class="BookFieldsExplain">Current Password</label>
                            <input type="password" class="form-control <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="current_password" name="current_password">
                            <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="BookFieldsExplain">New Password</label>
                            <input type="password" class="form-control <?php $__errorArgs = ['new_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="new_password" name="new_password">
                            <?php $__errorArgs = ['new_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="new_password_confirmation" class="BookFieldsExplain">Confirm New Password</label>
                            <input type="password" class="form-control <?php $__errorArgs = ['new_password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="new_password_confirmation" name="new_password_confirmation">
                            <?php $__errorArgs = ['new_password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Update Password</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Subscription Section -->
            <div class="card mb-4" id="subscription-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Subscription</h3>
                </div>
                <div class="card-body">
                    <?php if($subscription): ?>
                        <div class="mb-4">
                            <h6 class="BookFieldsExplain">Current Plan:</h6>
                            <div class="p-3 bg-light rounded">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <?php if($subscription->isAlpha()): ?>
                                            <h5 class="mb-0">AUTEUR Alpha</h5>
                                            <p class="text-muted mb-0">$0 / Monthly (Free in Alpha)</p>
                                        <?php elseif($subscription->isEarlyBird()): ?>
                                            <h5 class="mb-0">AUTEUR Early Bird</h5>
                                            <p class="text-muted mb-0">$9 / Monthly</p>
                                        <?php elseif($subscription->isAuteurPlus()): ?>
                                            <h5 class="mb-0">AUTEUR+</h5>
                                            <p class="text-muted mb-0">$240 / Yearly ($10 per month)</p>
                                        <?php elseif($subscription->isCoreAuteur()): ?>
                                            <h5 class="mb-0">🎉 AUTEUR Core</h5>
                                            <p class="text-muted mb-0">$0 / Monthly (1st Year FREE, then $13/month)</p>
                                        <?php else: ?>
                                            <h5 class="mb-0">AUTEUR</h5>
                                            <p class="text-muted mb-0">$15 / Monthly</p>
                                        <?php endif; ?>
                                    </div>
                                    <span class="badge <?php echo e($subscription->status == 'active' ? 'bg-success' : 'bg-warning text-dark'); ?>">
                                        <?php echo e(ucfirst($subscription->status)); ?>

                                    </span>
                                </div>

                                <div class="mt-3">
                                    <h6>Plan Features:</h6>
                                    <ul class="list-unstyled">
                                        <?php if($subscription->isAuteurPlus()): ?>
                                            <li><i class="ri-check-line text-success me-2"></i>Add unlimited books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 7 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Advanced analytics</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Priority support</li>
                                        <?php elseif($subscription->isCoreAuteur()): ?>
                                            <li><i class="ri-check-line text-success me-2"></i>Add unlimited books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 7 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Full vault access</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Advanced analytics</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Priority support</li>
                                            <li><i class="ri-star-line text-warning me-2"></i>Core Auteur exclusive benefits</li>
                                        <?php elseif($subscription->isEarlyBird()): ?>
                                            <li><i class="ri-check-line text-success me-2"></i>Add up to 3 books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 5 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Full vault access</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Basic analytics</li>
                                        <?php elseif($subscription->isAlpha()): ?>
                                            <li><i class="ri-check-line text-success me-2"></i>Add up to 3 books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 3 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Basic analytics</li>
                                            <?php if(isset($vaultRestricted) && $vaultRestricted): ?>
                                                <li><i class="ri-close-line text-danger me-2"></i>Vault access restricted</li>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <li><i class="ri-check-line text-success me-2"></i>Add up to 3 books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 5 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Basic analytics</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>

                                <?php if($subscription->billing_period_end): ?>
                                    <div class="mt-3">
                                        <p class="mb-0"><strong>Next billing date:</strong> <?php echo e($subscription->billing_period_end->format('F j, Y')); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="d-flex mt-4">
                            <?php if($showCoreAuteurUpgrade): ?>
                                <div class="alert alert-info text-center mb-4" style="background-color: #e0f7fa; border-color: #b2ebf2; color: #007bff;">
                                    <p class="mb-2">Special Offer: Upgrade to AUTEUR Core!</p>
                                    <p class="mb-3">1-year Premium membership for FREE! ($0 Per Month For The 1st Year; then - $13.00/month)</p>
                                    <a href="<?php echo e(route('subscription.direct-checkout', 'auteur_core')); ?>" class="btn btn-primary">Upgrade to AUTEUR Core</a>
                                </div>
                            <?php elseif($showEarlyBirdOffer): ?>
                                <div class="alert alert-warning text-center mb-4">
                                    <p class="mb-2">Dear Auteur, unfortunately you seemingly have failed consistently to meet a Core Auteur Candidate activity condition of at least "<?php echo e($coreGroupCaa); ?>" active session(s) per week, so please kindly enjoy our AUTEUR Early Bird offer instead.</p>
                                    <a href="<?php echo e(route('subscription.direct-checkout', 'early_bird')); ?>" class="btn btn-primary">Upgrade to Early Bird Plan</a>
                                </div>
                            <?php elseif($subscription): ?>
                                <?php if($subscription->isAlpha()): ?>
                                    <?php if(isset($productionFlow) && $productionFlow === 'production'): ?>
                                        <a href="<?php echo e(route('subscription.direct-checkout', 'early_bird')); ?>" class="btn btn-primary me-2">Upgrade to Early Bird Plan</a>
                                    <?php else: ?>
                                        <button class="btn btn-secondary me-2" disabled>Upgrade your Plan</button>
                                        <small class="text-muted mt-2">Upgrades will be available in Production mode</small>
                                    <?php endif; ?>
                                <?php elseif($subscription->isEarlyBird()): ?>
                                    <?php if(isset($productionFlow) && $productionFlow === 'production'): ?>
                                        <a href="<?php echo e(route('subscription.direct-checkout', 'auteur_plus')); ?>" class="btn btn-primary me-2">Upgrade to AUTEUR+</a>
                                    <?php else: ?>
                                        <button class="btn btn-secondary me-2" disabled>AUTEUR+ Available in Production</button>
                                    <?php endif; ?>
                                <?php elseif(!$subscription->isAuteurPlus()): ?>
                                    <?php if(isset($productionFlow) && $productionFlow === 'production'): ?>
                                        <a href="<?php echo e(route('subscription.direct-checkout', 'auteur_plus')); ?>" class="btn btn-primary me-2">Upgrade to AUTEUR+</a>
                                    <?php else: ?>
                                        <button class="btn btn-secondary me-2" disabled>AUTEUR+ Available in Production</button>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if($subscription->status == 'active' && !$subscription->isAlpha()): ?>
                                    <div class="d-flex">
                                        <form action="<?php echo e(route('subscription.cancel')); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                                Cancel Subscription
                                            </button>
                                        </form>
                                    </div>
                                <?php elseif($subscription->status == 'canceled' || $subscription->status == 'cancellation_requested'): ?>
                                    <div class="d-flex">
                                        <a href="<?php echo e(route('subscription.plans')); ?>" class="btn btn-primary">Reactivate Subscription</a>
                                    </div>
                                <?php endif; ?>
                            <?php elseif($hasAnySubscription): ?>
                                <div class="text-center p-4 bg-light rounded mb-4">
                                    <p class="mb-4">Your subscription has been canceled.</p>
                                    <div class="mt-4 d-flex justify-content-center">
                                        <a href="<?php echo e(route('subscription.direct-checkout', 'auteur')); ?>" class="btn btn-primary">Reactivate Subscription</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="text-center p-4 bg-light rounded mb-4">
                                    <p class="mb-4">You don't have an active subscription.</p>
                                    <div class="mt-4">
                                        <a href="<?php echo e(route('subscription.direct-checkout', 'auteur')); ?>" class="btn btn-primary me-2">Subscribe to AUTEUR</a>
                                        <?php if(isset($productionFlow) && $productionFlow === 'production'): ?>
                                            <a href="<?php echo e(route('subscription.direct-checkout', 'auteur_plus')); ?>" class="btn btn-success">Subscribe to AUTEUR+</a>
                                        <?php else: ?>
                                            <button class="btn btn-secondary" disabled>AUTEUR+ Available in Production</button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                </div>
            </div>

            <!-- Payment Method Section -->
            <div class="card mb-4" id="payment-method-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Payment Method</h3>
                </div>
                <div class="card-body">
                    <?php if($subscription && $subscription->status == 'active'): ?>
                        <div class="mb-4">
                            <h6 class="BookFieldsExplain">Current Payment Method:</h6>
                            <div class="p-3 bg-light rounded">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="ri-bank-card-line" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-0">•••• •••• •••• 4242</p>
                                        <p class="text-muted mb-0">Expires: 12/2025</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <form id="update-payment-form">
                                <div class="mb-3">
                                    <label for="cardholderName" class="BookFieldsExplain">Cardholder's Name</label>
                                    <input type="text" class="form-control" id="cardholderName" name="cardholderName">
                                </div>
                                <div class="mb-3">
                                    <label for="cardNumber" class="BookFieldsExplain">Card</label>
                                    <input type="text" class="form-control" id="cardNumber" name="cardNumber" placeholder="•••• •••• •••• ••••">
                                </div>
                                <div class="mb-3">
                                    <label for="zipCode" class="BookFieldsExplain">ZIP / Postal Code</label>
                                    <input type="text" class="form-control" id="zipCode" name="zipCode">
                                </div>
                                <button type="button" id="update-payment-button" class="btn btn-primary" style="background-color: #3f51b5; border-color: #3f51b5;">
                                    Update Payment Method
                                </button>
                            </form>
                            <p class="text-muted mt-2"><small>Your payment information is securely processed by Paddle.</small></p>
                            <?php if(config('services.paddle.sandbox', true)): ?>
                            <div class="alert alert-info mt-3">
                                <i class="ri-information-line me-2"></i>
                                <strong>Sandbox Mode</strong>
                                <p class="mb-0 mt-1">In sandbox mode, payment method updates have limited functionality. This will work correctly in production.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
                        <script>
                            // Clear any form fields on page load
                            document.addEventListener('DOMContentLoaded', function() {
                                const formFields = document.querySelectorAll('#update-payment-form input');
                                formFields.forEach(field => {
                                    field.value = '';
                                });
                            });

                            document.getElementById('update-payment-button').addEventListener('click', function() {
                                // Get the subscription ID
                                const subscriptionId = '<?php echo e($subscription->paddle_subscription_id ?? ""); ?>';

                                if (!subscriptionId) {
                                    showErrorModal('Unable to update payment method. Please contact support.');
                                    return;
                                }

                                // Initialize Paddle
                                Paddle.Environment.set('sandbox');
                                Paddle.Setup({
                                    token: '<?php echo e(config("services.paddle.client_token")); ?>',
                                    eventCallback: function(event) {
                                        console.log('Paddle event:', event.name, event);
                                    }
                                });

                                // Make a request to get a transaction for updating payment method
                                fetch('/subscription/update-payment/' + subscriptionId)
                                    .then(response => response.json())
                                    .then(data => {
                                        // Check for sandbox limitation
                                        if (data.sandbox_limitation) {
                                            // Show a more informative message for sandbox limitations
                                            showSandboxLimitationModal(data.message || 'Payment method updates are limited in sandbox mode.');
                                            return;
                                        }

                                        if (data.success && data.transaction_id) {
                                            // Open Paddle Checkout with the transaction ID
                                            Paddle.Checkout.open({
                                                transactionId: data.transaction_id,
                                                settings: {
                                                    displayMode: 'overlay',
                                                    theme: 'light',
                                                    locale: 'en',
                                                    successUrl: window.location.href
                                                }
                                            });
                                        } else {
                                            throw new Error(data.message || 'Unable to update payment method');
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error:', error);
                                        showErrorModal('Unable to update payment method. Please contact support.');
                                    });
                            });

                            function showErrorModal(message) {
                                // Show custom modal for error
                                const modalHtml = `
                                    <div class="modal fade" id="paymentErrorModal" tabindex="-1" aria-labelledby="paymentErrorModalLabel" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="paymentErrorModalLabel">localhost:8000</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>${message}</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" style="background-color: #17a2b8; border-color: #17a2b8;">OK</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // Add modal to the document
                                const modalContainer = document.createElement('div');
                                modalContainer.innerHTML = modalHtml;
                                document.body.appendChild(modalContainer.firstElementChild);

                                // Show the modal
                                const modal = new bootstrap.Modal(document.getElementById('paymentErrorModal'));
                                modal.show();
                            }

                            function showSandboxLimitationModal(message) {
                                // Show custom modal for sandbox limitations
                                const modalHtml = `
                                    <div class="modal fade" id="sandboxLimitationModal" tabindex="-1" aria-labelledby="sandboxLimitationModalLabel" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="sandboxLimitationModalLabel">Sandbox Mode Limitation</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="alert alert-info mb-3">
                                                        <i class="ri-information-line me-2"></i>
                                                        <strong>Sandbox Mode</strong>
                                                    </div>
                                                    <p>${message}</p>
                                                    <p class="mt-2">In the Paddle sandbox environment, payment method updates are not fully supported. This functionality will work correctly in production mode.</p>
                                                    <p class="mt-2">For testing purposes, you can use the Customer Portal links in the Paddle dashboard to manage subscriptions.</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // Add modal to the document
                                const modalContainer = document.createElement('div');
                                modalContainer.innerHTML = modalHtml;
                                document.body.appendChild(modalContainer.firstElementChild);

                                // Show the modal
                                const modal = new bootstrap.Modal(document.getElementById('sandboxLimitationModal'));
                                modal.show();
                            }
                        </script>
                    <?php else: ?>
                        <div class="text-center p-4 bg-light rounded">
                            <p>No payment method on file.</p>
                            <p class="text-muted">A payment method will be added when you subscribe to a plan.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Invoices History Section -->
            <div class="card mb-4" id="invoices-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Invoices History</h3>
                </div>
                <div class="card-body">
                    <?php if($subscription): ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><?php echo e($subscription->created_at->format('M d, Y')); ?></td>
                                        <td>
                                            <?php if($isAuteurPlus): ?>
                                                Auteur+ Subscription
                                            <?php elseif($subscription && $subscription->isCoreAuteur()): ?>
                                                Auteur Core Subscription
                                            <?php elseif($isEarlyBird): ?>
                                                Auteur Early Bird Subscription
                                            <?php else: ?>
                                                Auteur Subscription
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($isAuteurPlus): ?>
                                                $13.00
                                            <?php elseif($subscription && $subscription->isCoreAuteur()): ?>
                                                $0.00
                                            <?php elseif($isEarlyBird): ?>
                                                $9.00
                                            <?php else: ?>
                                                $0.00
                                            <?php endif; ?>
                                        </td>
                                        <td><span class="badge bg-success">Paid</span></td>
                                        <td><a href="#" class="btn btn-sm btn-outline-secondary disabled">View</a></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center p-4 bg-light rounded">
                            <p>No invoices available.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling
        document.querySelectorAll('.list-group-item').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all links
                document.querySelectorAll('.list-group-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Add active class to clicked link
                this.classList.add('active');

                // Smooth scroll to section
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                window.scrollTo({
                    top: targetElement.offsetTop - 20,
                    behavior: 'smooth'
                });
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/profile/index.blade.php ENDPATH**/ ?>