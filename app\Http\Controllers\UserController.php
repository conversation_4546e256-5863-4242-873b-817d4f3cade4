<?php

namespace App\Http\Controllers;

use App\Models\SystemControl;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Subscription;
use App\Services\SubscriptionFlowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;
use Exception;
use App\Http\Controllers\NotificationController;

class UserController extends Controller
{
    protected $flowService;

    public function __construct(SubscriptionFlowService $flowService)
    {
        $this->flowService = $flowService;
    }

    // To View Login Page
    public function loginView(){
        return view('auth.login');
    }

    // To View Register Page
    public function registerView(Request $request){
        // Check the current flow
        $currentFlow = $this->flowService->getFlow();

        // In Production mode, redirect to plan selection page if no plan is specified
        if ($currentFlow === 'production' && !$request->has('plan')) {
            return redirect()->route('subscription.public-plans');
        }

        $plan = $request->query('plan', 'auteur'); // Default to 'auteur' if no plan specified
        return view('auth.register', ['plan' => $plan]);
    }

    // To Create A New User
    public function registerUser(Request $req){

        $req->validate([
            'fullName'=> 'required|string|max:255',
            'email'=>'required|string|email|max:255|unique:users,email',
            'password'=>'required|string|min:8',
            'zip'=>'required|string|max:20'
        ], [
            'email.unique' => 'This email address is already registered. Please use a different email or login to your account.',
            'fullName.required' => 'Please enter your full name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'password.required' => 'Please enter a password.',
            'password.min' => 'Your password must be at least 8 characters long.'
        ]);

        // Get the selected plan
        $plan = $req->input('plan', 'auteur');

        // Set book and review limits based on plan
        $bookLimit = ($plan === 'auteur_plus') ? 999 : 3; // Unlimited for AUTEUR+, 3 for AUTEUR
        $reviewLimit = ($plan === 'auteur_plus') ? 7 : 3; // 7 per week for AUTEUR+, 3 for AUTEUR

        // Create a new user
        $newUser = new User;
        $newUser->fullName = $req->fullName;
        $newUser->email = $req->email;
        $newUser->password = Hash::make($req->password);
        $newUser->zipCode = $req->zip;
        $newUser->book_limit = $bookLimit;
        $newUser->review_limit_per_week = $reviewLimit;

        // Core Group Feature Logic for new registrations
        $systemControl = SystemControl::first();
        $isCoreGroupActive = $systemControl && $systemControl->core_group_active;
        $coreGroupCan = $systemControl ? $systemControl->core_group_can : 0;
        $coreGroupRegisteredCount = $systemControl ? $systemControl->core_group_registered_count : 0;

        if ($isCoreGroupActive && $coreGroupRegisteredCount < $coreGroupCan) {
            $newUser->is_core_auteur_candidate = true;
            $systemControl->core_group_registered_count++;
            if ($systemControl->core_group_registered_count >= $coreGroupCan) {
                $systemControl->core_group_active = false; // Automatically disable feature
            }
            $systemControl->save();
        }
        $newUser->save();

        // Create a wallet record for the new user and set the initial balance to default
        $wallet = new Wallet;
        $wallet->userId = $newUser->id; // Assuming 'user_id' is the foreign key linking users and wallets

        $signUpBonus = SystemControl::where('key_type', 'SignUpBonus')->first();

        $wallet->currentBalance = $signUpBonus->value;
        $wallet->save();

        // Log in the user automatically
        Auth::login($newUser);

        // Check the current subscription flow
        $currentFlow = $this->flowService->getFlow();

        // In Alpha flow, create a free subscription without Paddle checkout
        if ($currentFlow === 'alpha') {
            // Always create an Alpha subscription for the user in Alpha flow, regardless of the plan parameter
            $this->flowService->createAlphaSubscription($newUser);

            Log::info('Created Alpha subscription for new user', [
                'user_id' => $newUser->id,
                'plan' => 'alpha'
            ]);

            // Redirect to author page
            $redirect = redirect()->route('author');

            // Set flash messages in the desired order
            session()->flash('success', 'Registration successful! Your AUTEUR Alpha subscription has been activated.');
            if ($newUser->is_core_auteur_candidate) {
                session()->flash('core_auteur_message', 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.*<br><br><i>*active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.</i>');
            }
            session()->flash('welcome_message', 'Dear Auteur! Please <a href="' . route('book.create') . '">publish</a> your first book to start and add your "Amazon Reviewer Name" in your <a href="' . route('profile.index') . '">profile</a>.');

            return $redirect;
        }

        // For Production flow or AUTEUR+ plan, redirect to Paddle checkout
        if ($currentFlow === 'production' || $plan === 'auteur_plus') {
            // Determine which price ID to use based on the plan
            $priceId = $plan === 'auteur_plus'
                ? config('services.paddle.prices.auteur_plus')
                : config('services.paddle.prices.auteur');

            // Create session variables to store the plan information
            session(['selected_plan' => $plan]);

            // Set flash messages in the desired order
            // The 'success' message will be handled by SubscriptionController::success
            if ($newUser->is_core_auteur_candidate) {
                session()->flash('core_auteur_message', 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.*<br><br><i>*active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.</i>');
            }
            session()->flash('welcome_message', 'Dear Auteur! Please <a href="' . route('book.create') . '">publish</a> your first book to start and add your "Amazon Reviewer Name" in your <a href="' . route('profile.index') . '">profile</a>.');

            // Redirect to auto-checkout page to initiate Paddle checkout
            return redirect()->route('subscription.direct-checkout', $plan);
        }

        // Fallback: redirect to author page with a generic success message
        session()->flash('success', 'Registration successful! Your subscription has been activated successfully!');
        return redirect()->route('author');
    }


    public function loginUser(Request $request){
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            // Authentication passed...
            $request->session()->regenerate();

            // Get the authenticated user
            $user = Auth::user();

            // Update weekly login count
            $currentWeek = now()->startOfWeek();
            if ($user->last_login_week === null || $user->last_login_week->lt($currentWeek)) {
                $user->weekly_logins = 1;
                $user->last_login_week = $currentWeek;
            } else {
                $user->weekly_logins++;
            }
            $user->save(); // Save the updated login tracking

            // Mark notifications as read after login
            $notificationController = new NotificationController();
            $notificationController->markAsRead(new Request()); // Pass a new Request instance

            Log::info("User logged in and notifications marked as read", ['user_id' => $user->id, 'weekly_logins' => $user->weekly_logins]);

            // Check if the user should see the production warning message
            if ($this->flowService->shouldShowProductionWarning($user)) {
                // Check if we're in production mode
                if ($this->flowService->isProductionFlow()) {
                    $systemControl = SystemControl::first();
                    $coreAuteurActivityThreshold = $systemControl ? $systemControl->core_group_caa : 0;

                    if ($user->is_core_auteur_candidate) {
                        if ($user->weekly_logins >= $coreAuteurActivityThreshold) {
                            // User met the Core Auteur condition
                            session()->flash('info', 'Upgrade to AUTEUR Core');
                            session()->flash('core_auteur_upgrade_offer', true);
                            Log::info('Offering AUTEUR Core upgrade to user', ['user_id' => $user->id]);
                        } else {
                            // User failed the Core Auteur condition
                            $message = "Dear Auteur, unfortunately you seemingly have failed to meet a Core Auteur Candidate activity condition of at least \"{$coreAuteurActivityThreshold}\" active session(s) per week, so please kindly enjoy our AUTEUR Early Bird offer instead.";
                            session()->flash('warning', $message);
                            session()->flash('early_bird_offer', true);
                            Log::info('Offering Early Bird to user due to failed Core Auteur condition', ['user_id' => $user->id, 'message' => $message]);
                        }
                    } else {
                        // Default production mode message for Alpha users who haven't upgraded
                        $message = "Dear Auteur, we`ve switched to Production mode, as an Early Access (Alpha) User please upgrade Your Subscription at a special price!";
                        session()->flash('warning', $message);
                        Log::info('Showing general production mode warning to Alpha user after login', ['user_id' => $user->id, 'message' => $message]);
                    }

                    return redirect()->intended('/dashboard');
                } else {
                    // Pre-production countdown message
                    $remainingDays = $this->flowService->getProductionCountdownRemainingDays();
                    $message = "Dear Auteur, we are going to switch to Production mode {$remainingDays} and as an Early Access (Alpha) User you will be able to upgrade at a special price!";

                    Log::info('Showing production countdown warning to user after login', [
                        'user_id' => $user->id,
                        'message' => $message,
                        'remaining_days' => $remainingDays
                    ]);

                    // Redirect with the info message (blue styling)
                    return redirect()->intended('/dashboard')->with('info', $message);
                }
            }

            // Redirect to intended URL or dashboard if no intended URL is set
            return redirect()->intended('/dashboard');
        } else {
            return redirect()->route('login')->with('error', 'Invalid credentials');
        }
    }

    public function logout(Request $request)
    {
        // Get the user ID before logging out
        $userId = Auth::id();

        if ($userId) {
            Log::info("User logging out", ['user_id' => $userId]);
        }

        Auth::logout(); // Log the user out

        // Invalidate the session and regenerate the CSRF token
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home'); // Redirect to the home page instead of login
    }

    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google callback after authentication.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if user already exists
            $user = User::where('email', $googleUser->email)->first();

            if (!$user) {
                // Create new user if doesn't exist
                $user = new User;
                $user->fullName = $googleUser->name;
                $user->email = $googleUser->email;
                $user->google_id = $googleUser->id;
                // Generate a random password for the user
                $user->password = Hash::make(Str::random(16));
                $user->zipCode = '00000'; // Default zip code

                // Core Group Feature Logic for new Google registrations
                $systemControl = SystemControl::first();
                $isCoreGroupActive = $systemControl && $systemControl->core_group_active;
                $coreGroupCan = $systemControl ? $systemControl->core_group_can : 0;
                $coreGroupRegisteredCount = $systemControl ? $systemControl->core_group_registered_count : 0;

                if ($isCoreGroupActive && $coreGroupRegisteredCount < $coreGroupCan) {
                    $user->is_core_auteur_candidate = true;
                    $systemControl->core_group_registered_count++;
                    if ($systemControl->core_group_registered_count >= $coreGroupCan) {
                        $systemControl->core_group_active = false; // Automatically disable feature
                    }
                    $systemControl->save();
                }
                $user->save();

                // Create a wallet for the new user
                $wallet = new Wallet;
                $wallet->userId = $user->id;

                $signUpBonus = SystemControl::where('key_type', 'SignUpBonus')->first();
                $wallet->currentBalance = $signUpBonus->value;
                $wallet->save();

                // Log in the user
                Auth::login($user);

                // Check the current subscription flow
                $currentFlow = $this->flowService->getFlow();

                // In Alpha flow, create a free subscription without Paddle checkout
                if ($currentFlow === 'alpha') {
                    // Create an Alpha subscription for the user
                    $this->flowService->createAlphaSubscription($user);

                    Log::info('Created Alpha subscription for new Google user', [
                        'user_id' => $user->id,
                        'plan' => 'alpha'
                    ]);

                    // Redirect to author page
                    $redirect = redirect()->route('author');

                    // Set flash messages in the desired order
                    session()->flash('success', 'Registration successful! Your AUTEUR Alpha subscription has been activated.');
                    if ($user->is_core_auteur_candidate) {
                        session()->flash('core_auteur_message', 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.*<br><br><i>*active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.</i>');
                    }
                    session()->flash('welcome_message', 'Dear Auteur! Please <a href="' . route('book.create') . '">publish</a> your first book to start and add your "Amazon Reviewer Name" in your <a href="' . route('profile.index') . '">profile</a>.');

                    return $redirect;
                }

                // In Production flow, redirect to subscription plans page
                $redirect = redirect()->route('subscription.plans');

                // Set flash messages in the desired order
                session()->flash('success', 'Welcome! Please complete your registration by initiating your AUTEUR plan.');
                if ($user->is_core_auteur_candidate) {
                    session()->flash('core_auteur_message', 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.*<br><br><i>*active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.</i>');
                }
                session()->flash('welcome_message', 'Dear Auteur! Please <a href="' . route('book.create') . '">publish</a> your first book to start and add your "Amazon Reviewer Name" in your <a href="' . route('profile.index') . '">profile</a>.');

                return $redirect;
            } else {
                // Update Google ID if it's not set
                if (!$user->google_id) {
                    $user->google_id = $googleUser->id;
                    $user->save();
                }

                // Login the user
                Auth::login($user);

                Log::info("Google user logged in", ['user_id' => $user->id]);

                return redirect()->intended('/dashboard');
            }

        } catch (Exception $e) {
            return redirect()->route('login')->with('error', 'Google authentication failed: ' . $e->getMessage());
        }
    }
}
